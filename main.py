#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主程式 - 按順序執行大專院校資料處理流程
1. 爬取大專院校資料
2. 清理和整合資料
3. 上傳到Azure Cosmos DB
"""

import sys
import time
import traceback
import subprocess
from datetime import datetime

def 執行程式(程式名稱, 模組名稱):
    """
    執行指定的程式模組

    Args:
        程式名稱: 程式的中文名稱，用於顯示
        模組名稱: 要匯入和執行的模組名稱

    Returns:
        bool: 執行成功返回True，失敗返回False
    """
    print(f"\n{'='*60}")
    print(f"🚀 開始執行: {程式名稱}")
    print(f"{'='*60}")

    start_time = time.time()

    try:
        # 使用 subprocess 執行各個程式檔案，確保完整的執行環境
        # 設定環境變數確保正確的編碼處理
        import os
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'

        if 模組名稱 == "大專院校爬蟲":
            result = subprocess.run([sys.executable, "大專院校爬蟲.py"],
                                  capture_output=True, text=True,
                                  encoding='utf-8', errors='replace', env=env)

        elif 模組名稱 == "資料清理":
            result = subprocess.run([sys.executable, "資料清理.py"],
                                  capture_output=True, text=True,
                                  encoding='utf-8', errors='replace', env=env)

        elif 模組名稱 == "AzureCosmos":
            result = subprocess.run([sys.executable, "AzureCosmos.py"],
                                  capture_output=True, text=True,
                                  encoding='utf-8', errors='replace', env=env)

        else:
            print(f"❌ 未知的模組名稱: {模組名稱}")
            return False

        # 顯示程式輸出（如果有的話）
        if result.stdout and result.stdout.strip():
            print("📄 程式輸出:")
            print(result.stdout)

        # 檢查是否有錯誤
        if result.returncode != 0:
            if result.stderr and result.stderr.strip():
                print(f"❌ 錯誤輸出:")
                print(result.stderr)
            raise subprocess.CalledProcessError(result.returncode, result.args)

        end_time = time.time()
        execution_time = end_time - start_time

        print(f"\n✅ {程式名稱} 執行完成")
        print(f"⏱️  執行時間: {execution_time:.2f} 秒")

        return True

    except FileNotFoundError as e:
        print(f"❌ 找不到檔案 {模組名稱}.py")
        print(f"請確認檔案存在於當前目錄中")
        return False

    except subprocess.CalledProcessError as e:
        print(f"❌ 執行 {程式名稱} 時發生錯誤:")
        print(f"程式返回碼: {e.returncode}")
        return False

    except Exception as e:
        print(f"❌ 執行 {程式名稱} 時發生錯誤:")
        print(f"錯誤類型: {type(e).__name__}")
        print(f"錯誤訊息: {str(e)}")
        print(f"詳細錯誤資訊:")
        traceback.print_exc()
        return False

def 主程序():
    """
    主程序 - 按順序執行所有程式
    """
    # 記錄總開始時間
    總開始時間 = time.time()
    開始時間字串 = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 定義要執行的程式清單 (程式名稱, 模組名稱)
    程式清單 = [
        ("大專院校爬蟲", "大專院校爬蟲"),
        ("資料清理", "資料清理"),
        ("Azure Cosmos DB 上傳", "AzureCosmos")
    ]

    成功計數 = 0
    總程式數 = len(程式清單)
    
    for i, (程式名稱, 模組名稱) in enumerate(程式清單, 1):
        print(f"\n📋 步驟 {i}/{總程式數}: {程式名稱}")
        
        # 執行程式
        if 執行程式(程式名稱, 模組名稱):
            成功計數 += 1
            print(f"✅ 步驟 {i} 完成")
        else:
            print(f"❌ 步驟 {i} 失敗")
            
            # 詢問是否繼續執行下一個程式
            while True:
                choice = input(f"\n⚠️  {程式名稱} 執行失敗，是否繼續執行下一個程式？ (y/n): ").lower().strip()
                if choice in ['y', 'yes', '是']:
                    print("⏭️  繼續執行下一個程式...")
                    break
                elif choice in ['n', 'no', '否']:
                    print("🛑 使用者選擇停止執行")
                    break
                else:
                    print("請輸入 y (是) 或 n (否)")
            
            if choice in ['n', 'no', '否']:
                break
    
    # 計算總花費時間
    總結束時間 = time.time()
    總花費時間 = 總結束時間 - 總開始時間
    結束時間字串 = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 格式化總花費時間
    小時 = int(總花費時間 // 3600)
    分鐘 = int((總花費時間 % 3600) // 60)
    秒數 = 總花費時間 % 60

    if 小時 > 0:
        時間格式 = f"{小時}小時{分鐘}分鐘{秒數:.1f}秒"
    elif 分鐘 > 0:
        時間格式 = f"{分鐘}分鐘{秒數:.1f}秒"
    else:
        時間格式 = f"{秒數:.1f}秒"

    # 顯示執行結果摘要
    print(f"\n{'='*60}")
    print("📊 執行結果摘要")
    print(f"{'='*60}")
    print(f"成功執行: {成功計數}")
    print(f"失敗數量: {總程式數 - 成功計數}")
    print(f"⏱️  總花費時間: {時間格式}")

    if 成功計數 == 總程式數:
        print("🎉 所有程式執行完成！")
    else:
        print("⚠️  部分程式執行失敗，請檢查錯誤訊息")

if __name__ == "__main__":
    try:
        主程序()
    except KeyboardInterrupt:
        print("\n\n⚠️  使用者中斷執行 (Ctrl+C)")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 主程序發生未預期錯誤: {e}")
        traceback.print_exc()
        sys.exit(1)
    
    print("\n👋 程式結束")
