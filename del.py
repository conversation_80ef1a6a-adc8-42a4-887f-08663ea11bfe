import os
import sys
from azure.cosmos import CosmosClient, PartitionKey
from azure.cosmos.exceptions import CosmosResourceExistsError, CosmosResourceNotFoundError

# --- 請在這裡設定您的連線資訊 ---
# 建議從環境變數讀取，以策安全
ENDPOINT = os.environ.get("COSMOS_DB_ENDPOINT", "YOUR_COSMOS_DB_ENDPOINT")
KEY = os.environ.get("COSMOS_DB_KEY", "YOUR_COSMOS_DB_KEY")
DATABASE_NAME = "YOUR_DATABASE_NAME"
CONTAINER_NAME = "YOUR_CONTAINER_NAME"

# !!! 極度重要：請將 'your_partition_key_field' 替換成您容器真正的分割區索引鍵欄位名稱 !!!
# 例如，如果您的分割區索引鍵是 'userId'，就寫 'userId'
PARTITION_KEY_FIELD = "your_partition_key_field"

def fix_item_ids_with_slash():
    """
    連接到 Cosmos DB，找出 ID 中包含 '/' 的項目，
    建立一個使用 '_' 的新項目，然後利用 SDK 的能力刪除包含 '/' 的舊項目。
    """
    if any(val.startswith("YOUR_") for val in [ENDPOINT, KEY, DATABASE_NAME, CONTAINER_NAME]) or PARTITION_KEY_FIELD == "your_partition_key_field":
        print("錯誤：請在執行前，先在腳本中設定您的連線資訊和分割區索引鍵欄位名稱。")
        sys.exit(1)

    # 初始化 Cosmos DB 用戶端
    client = CosmosClient(ENDPOINT, credential=KEY)
    database = client.get_database_client(DATABASE_NAME)
    container = database.get_container_client(CONTAINER_NAME)

    print(f"成功連線到資料庫 '{DATABASE_NAME}' 的容器 '{CONTAINER_NAME}'。")
    print(f"將使用 '{PARTITION_KEY_FIELD}' 作為分割區索引鍵欄位。")

    # 查詢所有 ID 中包含 '/' 的項目
    query = "SELECT * FROM c WHERE CONTAINS(c.id, '/')"
    
    try:
        items_to_fix = list(container.query_items(query=query, enable_cross_partition_query=True))
    except Exception as e:
        print(f"查詢時發生錯誤: {e}")
        return

    if not items_to_fix:
        print("太好了！找不到任何 ID 中包含 '/' 的項目，無需執行任何操作。")
        return

    print(f"找到 {len(items_to_fix)} 個需要修正的項目。開始處理...")

    success_count = 0
    fail_count = 0

    # 迭代處理每一個需要修正的項目
    for item in items_to_fix:
        old_id = item['id']
        
        # 再次檢查，避免意外
        if '/' not in old_id:
            continue

        new_id = old_id.replace('/', '_')
        
        try:
            partition_key_value = item[PARTITION_KEY_FIELD]
        except KeyError:
            print(f"\n錯誤：項目 '{old_id}' 中找不到分割區索引鍵欄位 '{PARTITION_KEY_FIELD}'。跳過此項目。")
            fail_count += 1
            continue

        print(f"\n處理中: '{old_id}' -> '{new_id}' (分割區索引鍵: {partition_key_value})")

        try:
            # 建立一個新副本，並將 ID 更新為新的 ID
            new_item_body = item.copy()
            new_item_body['id'] = new_id

            # 步驟 A: 建立新項目
            print(f"  - 步驟 1/2: 正在建立新項目 '{new_id}'...")
            container.create_item(body=new_item_body)
            print(f"      成功建立。")

            # 步驟 B: 刪除舊項目
            print(f"  - 步驟 2/2: 正在刪除舊項目 '{old_id}'...")
            
            # --- 關鍵之處 ---
            # 現代版的 Python SDK 會自動處理 ID 中的特殊字元 (例如'/')，
            # 將其正確地進行 URL 編碼後再發送請求給伺服器。
            # 這就是為什麼這個腳本可以成功刪除舊項目的原因。
            container.delete_item(item=old_id, partition_key=partition_key_value)
            print(f"      成功刪除。")

            success_count += 1

        except CosmosResourceExistsError:
            print(f"  - 警告：ID 為 '{new_id}' 的項目已存在。可能之前已修正過。")
            print(f"      正在嘗試直接刪除殘留的舊項目 '{old_id}'...")
            try:
                container.delete_item(item=old_id, partition_key=partition_key_value)
                print(f"      成功刪除殘留的舊項目。")
                success_count += 1
            except CosmosResourceNotFoundError:
                 print(f"      舊項目 '{old_id}' 已經不存在。無需操作。")
                 success_count += 1
            except Exception as e_del:
                print(f"      刪除殘留項目時失敗: {e_del}")
                fail_count += 1
        except Exception as e:
            print(f"  - 處理 '{old_id}' 時發生未知錯誤: {e}")
            fail_count += 1
            
    print("\n--- 處理完成 ---")
    print(f"成功修正: {success_count} 個")
    print(f"失敗: {fail_count} 個")

if __name__ == '__main__':
    fix_item_ids_with_slash()