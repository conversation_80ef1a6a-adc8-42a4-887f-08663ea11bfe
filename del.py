import sys
from azure.cosmos import CosmosClient
from azure.cosmos.exceptions import CosmosResourceExistsError, CosmosResourceNotFoundError

# --- Azure Cosmos DB 連線資訊 ---
# 從 AzureCosmos.py 參考的連線資訊
ENDPOINT = "https://yuntech-japan-nosql.documents.azure.com:443/"
KEY = "****************************************************************************************"
DATABASE_NAME = "embedding"

# 預設設定 - 請修改為您要處理的容器
CONTAINER_NAME = "knowledges"
PARTITION_KEY_FIELD = "title"  # 所有容器都使用年度作為分割區索引鍵

def get_cosmos_client():
    """
    建立並返回 Cosmos DB 客戶端連線
    """
    # 檢查連線資訊是否已正確設定
    if not ENDPOINT or not KEY or not DATABASE_NAME or not CONTAINER_NAME:
        print("錯誤：請確認所有連線資訊都已正確設定。")
        sys.exit(1)

    print(f"連線資訊:")
    print(f"  - 端點: {ENDPOINT}")
    print(f"  - 資料庫: {DATABASE_NAME}")
    print(f"  - 容器: {CONTAINER_NAME}")
    print(f"  - 分割區索引鍵欄位: {PARTITION_KEY_FIELD}")
    print()

    # 初始化 Cosmos DB 用戶端
    client = CosmosClient(ENDPOINT, credential=KEY)
    database = client.get_database_client(DATABASE_NAME)
    container = database.get_container_client(CONTAINER_NAME)

    print(f"成功連線到資料庫 '{DATABASE_NAME}' 的容器 '{CONTAINER_NAME}'。")
    print(f"將使用 '{PARTITION_KEY_FIELD}' 作為分割區索引鍵欄位。")
    print()

    return container

def search_specific_id(container, search_id):
    """
    搜尋特定 ID 的文件

    Args:
        container: Cosmos DB 容器客戶端
        search_id: 要搜尋的 ID

    Returns:
        找到的文件或 None
    """
    try:
        query = f"SELECT * FROM c WHERE c.id = '{search_id}'"
        items = list(container.query_items(query=query, enable_cross_partition_query=True))

        if items:
            return items[0]
        else:
            return None

    except Exception as e:
        print(f"搜尋 ID '{search_id}' 時發生錯誤: {e}")
        return None

def search_ids_with_slash(container):
    """
    搜尋所有 ID 中包含 '/' 字元的文件

    Args:
        container: Cosmos DB 容器客戶端

    Returns:
        包含 '/' 的文件列表
    """
    try:
        query = "SELECT * FROM c WHERE CONTAINS(c.id, '/')"
        items = list(container.query_items(query=query, enable_cross_partition_query=True))
        return items

    except Exception as e:
        print(f"搜尋包含 '/' 的 ID 時發生錯誤: {e}")
        return []

def display_item_info(item):
    """
    顯示文件資訊（僅顯示 ID 和 title）

    Args:
        item: 要顯示的文件
    """
    print(f"📄 文件資訊:")
    print(f"  - ID: {item.get('id', 'N/A')}")
    print(f"  - title: {item.get('title', 'N/A')}")
    print()

def fix_single_item(container, item):
    """
    修復單一文件的 ID（將 '/' 替換為 '_'）

    Args:
        container: Cosmos DB 容器客戶端
        item: 要修復的文件

    Returns:
        修復是否成功
    """
    old_id = item['id']

    if '/' not in old_id:
        print(f"ID '{old_id}' 中沒有包含 '/' 字元，無需修復。")
        return True

    new_id = old_id.replace('/', '_')

    try:
        partition_key_value = item[PARTITION_KEY_FIELD]
    except KeyError:
        print(f"錯誤：項目 '{old_id}' 中找不到分割區索引鍵欄位 '{PARTITION_KEY_FIELD}'。")
        return False

    print(f"開始修復: '{old_id}' -> '{new_id}' (分割區索引鍵: {partition_key_value})")

    try:
        # 建立一個新副本，並將 ID 更新為新的 ID
        new_item_body = item.copy()
        new_item_body['id'] = new_id

        # 步驟 1: 建立新項目
        print(f"  - 步驟 1/2: 正在建立新項目 '{new_id}'...")
        container.create_item(body=new_item_body)
        print(f"      ✅ 成功建立。")

        # 步驟 2: 刪除舊項目
        print(f"  - 步驟 2/2: 正在刪除舊項目 '{old_id}'...")
        # 使用原始項目物件來刪除，而不是只使用 ID 字串
        # 這樣可以避免 URL 編碼問題
        container.delete_item(item=item, partition_key=partition_key_value)
        print(f"      ✅ 成功刪除。")

        return True

    except CosmosResourceExistsError:
        print(f"  - ⚠️  警告：ID 為 '{new_id}' 的項目已存在。可能之前已修正過。")
        print(f"      正在嘗試直接刪除殘留的舊項目 '{old_id}'...")
        try:
            container.delete_item(item=old_id, partition_key=partition_key_value)
            print(f"      ✅ 成功刪除殘留的舊項目。")
            return True
        except CosmosResourceNotFoundError:
            print(f"      ℹ️  舊項目 '{old_id}' 已經不存在。無需操作。")
            return True
        except Exception as e_del:
            print(f"      ❌ 刪除殘留項目時失敗: {e_del}")
            return False
    except Exception as e:
        print(f"  - ❌ 處理 '{old_id}' 時發生未知錯誤: {e}")
        return False

def fix_multiple_items(container, items):
    """
    批量修復多個文件的 ID

    Args:
        container: Cosmos DB 容器客戶端
        items: 要修復的文件列表

    Returns:
        (成功數量, 失敗數量)
    """
    success_count = 0
    fail_count = 0

    print(f"開始批量處理 {len(items)} 個項目...")
    print("=" * 50)

    # 迭代處理每一個需要修正的項目
    for i, item in enumerate(items, 1):
        print(f"\n[{i}/{len(items)}] 處理項目:")

        if fix_single_item(container, item):
            success_count += 1
        else:
            fail_count += 1

    print("\n" + "=" * 50)
    print("--- 批量處理完成 ---")
    print(f"✅ 成功修正: {success_count} 個")
    print(f"❌ 失敗: {fail_count} 個")

    return success_count, fail_count

def get_user_confirmation(message):
    """
    獲取使用者確認

    Args:
        message: 確認訊息

    Returns:
        True 如果使用者確認，False 否則
    """
    while True:
        response = input(f"{message} (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            return True
        elif response in ['n', 'no', '否']:
            return False
        else:
            print("請輸入 'y' 或 'n'")

def mode_1_search_specific_id(container):
    """
    模式 1: 搜尋特定 ID
    """
    print("\n🔍 模式 1: 搜尋特定 ID")
    print("-" * 30)

    search_id = input("請輸入要搜尋的 ID: ").strip()

    if not search_id:
        print("❌ ID 不能為空")
        return

    print(f"\n正在搜尋 ID: '{search_id}'...")

    item = search_specific_id(container, search_id)

    if item:
        print("✅ 找到文件！")
        display_item_info(item)

        # 檢查是否包含 '/' 字元
        if '/' in item['id']:
            print("⚠️  此 ID 包含 '/' 字元，建議進行修復。")
            if get_user_confirmation("是否要進行資料修復流程？"):
                print("\n🔧 開始修復...")
                if fix_single_item(container, item):
                    print("🎉 修復完成！")
                else:
                    print("❌ 修復失敗")
        else:
            print("ℹ️  此 ID 沒有包含 '/' 字元，無需修復。")
    else:
        print(f"❌ 找不到 ID 為 '{search_id}' 的文件")

def mode_2_search_slash_ids(container):
    """
    模式 2: 搜尋所有包含 '/' 字元的 ID
    """
    print("\n🔍 模式 2: 搜尋所有包含 '/' 字元的 ID")
    print("-" * 40)

    print("正在搜尋包含 '/' 字元的文件...")

    items = search_ids_with_slash(container)

    if not items:
        print("✅ 太好了！找不到任何 ID 中包含 '/' 的項目。")
        return

    print(f"📊 找到 {len(items)} 個包含 '/' 字元的文件：")
    print("=" * 50)

    # 顯示所有找到的文件
    for i, item in enumerate(items, 1):
        print(f"\n[{i}] ID: {item['id']}")
        print(f"    title: {item.get('title', 'N/A')}")

    print("=" * 50)

    # 詢問是否要進行修復
    if get_user_confirmation(f"是否要對這 {len(items)} 個文件進行資料修復流程？"):
        print("\n🔧 開始批量修復...")
        fix_multiple_items(container, items)
    else:
        print("取消修復操作。")

def show_menu():
    """
    顯示主選單
    """
    print("\n" + "=" * 60)
    print("🛠️  Azure Cosmos DB ID 修復工具")
    print("=" * 60)
    print("請選擇操作模式：")
    print("  1️⃣  搜尋特定 ID")
    print("  2️⃣  搜尋所有包含 '/' 字元的 ID")
    print("  0️⃣  退出程式")
    print("-" * 60)

def main():
    """
    主程式
    """
    try:
        # 建立 Cosmos DB 連線
        container = get_cosmos_client()

        while True:
            show_menu()

            choice = input("請輸入選項 (0-2): ").strip()

            if choice == '1':
                mode_1_search_specific_id(container)
            elif choice == '2':
                mode_2_search_slash_ids(container)
            elif choice == '0':
                print("👋 程式結束，再見！")
                break
            else:
                print("❌ 無效的選項，請輸入 0、1 或 2")

            # 詢問是否繼續
            if choice in ['1', '2']:
                print("\n" + "-" * 40)
                if not get_user_confirmation("是否要繼續使用程式？"):
                    print("👋 程式結束，再見！")
                    break

    except KeyboardInterrupt:
        print("\n\n👋 程式被使用者中斷，再見！")
    except Exception as e:
        print(f"\n❌ 程式執行時發生錯誤: {e}")

# 保留舊的函數以維持向後兼容性
def fix_item_ids_with_slash():
    """
    舊版本的函數，直接執行模式 2 的功能
    """
    container = get_cosmos_client()
    mode_2_search_slash_ids(container)

if __name__ == '__main__':
    main()